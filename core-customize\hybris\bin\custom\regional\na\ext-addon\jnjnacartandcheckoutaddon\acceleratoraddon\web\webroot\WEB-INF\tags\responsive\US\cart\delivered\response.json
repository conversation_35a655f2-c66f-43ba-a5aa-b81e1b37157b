{"cartModifications": [{"statusCode": "Valid product code", "quantityAdded": 0, "quantity": 0, "entry": {"entryNumber": null, "quantity": null, "basePrice": null, "totalPrice": null, "product": {"code": "DK-200", "name": null, "url": null, "description": null, "purchasable": null, "stock": null, "futureStocks": null, "availableForPickup": null, "averageRating": null, "numberOfReviews": null, "summary": null, "manufacturer": null, "variantType": null, "price": null, "baseProduct": null, "images": null, "categories": null, "reviews": null, "classifications": null, "potentialPromotions": null, "variantOptions": null, "baseOptions": null, "volumePricesFlag": null, "volumePrices": null, "productReferences": null, "variantMatrix": null, "priceRange": null, "firstCategoryNameList": null, "multidimensional": null, "configurable": null, "configuratorType": null, "addToCartDisabled": null, "addToCartDisabledMessage": null, "tags": null, "sapUnit": null, "keywords": null, "gtin": null, "expiryDate": null, "status": null, "oldProductCode": null, "upc": null, "siteName": null, "saleableInd": null, "salesRepCompatibleInd": null, "errorMessage": null, "numerator": null, "firstShipEffective": null, "launchStatus": null, "salesUnit": null, "deliveryUnit": null, "salesUnitQty": null, "hazmatCode": null, "isProdViewable": false, "contract": null, "listPrice": null, "salesOrgCode": null, "mddSpecification": null, "consumerSpecification": null, "imageAvailableInd": null, "displayName": null, "discontinued": false, "baseMaterialNumber": null, "productWeight": null, "productVolume": null, "invalidProductCodes": null, "lastAddedProduct": null, "lastAddedProductQuantity": null, "obsolete": false, "descriptionText": null, "shortOverview": null, "franchiseName": null, "subBrand": null, "brandDescription": null, "kitType": null, "isKitProduct": null, "originCountry": null, "salesUom": null, "productType": null, "regionCode": null, "infieldInd": null, "availableInd": null, "eachQtyUom": null, "structuredClassifications": null, "classAttributeFeatureMap": null, "productModCode": null, "inventoryAvailabilityCode": null, "baseMaterialNumberWithoutLeadingZeros": null, "codeWithoutLeadingZeros": null, "literatureInd": null, "brandNameCustom": null, "caseSize": null, "minOrderQuantity": null, "nameCustom": null, "ndc11": null, "ndc10": null, "operatingCompany": null, "pharmaSpecification": null, "productTypes": null, "divested": false, "scheduled": false, "constructType": null, "productIncludeInfo": null, "requiredFeatures": null, "components": null, "internalRef": null, "vendorCode": null, "modStatus": null, "lotNumbers": null, "anchorQty": null, "anchorDynaUsed": null, "disposableShuttlesGrasperQty": null, "freeStrandDynaUsed": null, "implantsUsed": null, "intrafixUsed": null, "disposableACLQty": null, "knivesReamersUsed": null, "capitalDisposableQty": null, "coolPulseUSed": null, "materialType": null, "quantity": null, "shipToArriveDate": null, "totalShelfLife": null, "lvlOneCategories": null, "lvlTwoCategories": null, "materialStatus": null, "modProductMaterialStatus": null, "viewOnlyProduct": false, "favoriteKit": false, "nonFavoriteKit": false, "anticipatedDeliveryDate": null, "eligibleForAnticipatedDeliveryDate": false}, "updateable": false, "deliveryMode": null, "deliveryPointOfService": null, "entries": null, "configurationInfos": null, "statusSummaryMap": null, "entryGroupNumbers": null, "comments": null, "arrivalSlots": null, "supportedActions": null, "trackingNumAndUrl": null, "baseUOM": null, "salesUOM": null, "grossPrice": null, "discountsOnPrice": null, "shippingPoint": null, "totalFees": null, "freightFees": null, "handlingFee": null, "itemCategory": null, "higherLevelItemNo": null, "substitutionReason": null, "taxes": null, "dropShipFee": null, "netPrice": null, "billingDeliveryBlock": null, "minimumOrderFee": null, "priceType": null, "proNumber": null, "bolNumber": null, "carrier": null, "hsapromotion": null}, "deliveryModeChanged": null, "cartCode": null, "statusMessage": null, "entryGroupNumbers": null, "error": false}, {"statusCode": "Entered product code DK-9000 is invalid.  Please delete this entry or enter a valid product code, and try again.", "quantityAdded": 0, "quantity": 0, "entry": {"entryNumber": null, "quantity": null, "basePrice": null, "totalPrice": null, "product": {"code": "DK-9000", "name": null, "url": null, "description": null, "purchasable": null, "stock": null, "futureStocks": null, "availableForPickup": null, "averageRating": null, "numberOfReviews": null, "summary": null, "manufacturer": null, "variantType": null, "price": null, "baseProduct": null, "images": null, "categories": null, "reviews": null, "classifications": null, "potentialPromotions": null, "variantOptions": null, "baseOptions": null, "volumePricesFlag": null, "volumePrices": null, "productReferences": null, "variantMatrix": null, "priceRange": null, "firstCategoryNameList": null, "multidimensional": null, "configurable": null, "configuratorType": null, "addToCartDisabled": null, "addToCartDisabledMessage": null, "tags": null, "sapUnit": null, "keywords": null, "gtin": null, "expiryDate": null, "status": null, "oldProductCode": null, "upc": null, "siteName": null, "saleableInd": null, "salesRepCompatibleInd": null, "errorMessage": null, "numerator": null, "firstShipEffective": null, "launchStatus": null, "salesUnit": null, "deliveryUnit": null, "salesUnitQty": null, "hazmatCode": null, "isProdViewable": false, "contract": null, "listPrice": null, "salesOrgCode": null, "mddSpecification": null, "consumerSpecification": null, "imageAvailableInd": null, "displayName": null, "discontinued": false, "baseMaterialNumber": null, "productWeight": null, "productVolume": null, "invalidProductCodes": null, "lastAddedProduct": null, "lastAddedProductQuantity": null, "obsolete": false, "descriptionText": null, "shortOverview": null, "franchiseName": null, "subBrand": null, "brandDescription": null, "kitType": null, "isKitProduct": null, "originCountry": null, "salesUom": null, "productType": null, "regionCode": null, "infieldInd": null, "availableInd": null, "eachQtyUom": null, "structuredClassifications": null, "classAttributeFeatureMap": null, "productModCode": null, "inventoryAvailabilityCode": null, "baseMaterialNumberWithoutLeadingZeros": null, "codeWithoutLeadingZeros": null, "literatureInd": null, "brandNameCustom": null, "caseSize": null, "minOrderQuantity": null, "nameCustom": null, "ndc11": null, "ndc10": null, "operatingCompany": null, "pharmaSpecification": null, "productTypes": null, "divested": false, "scheduled": false, "constructType": null, "productIncludeInfo": null, "requiredFeatures": null, "components": null, "internalRef": null, "vendorCode": null, "modStatus": null, "lotNumbers": null, "anchorQty": null, "anchorDynaUsed": null, "disposableShuttlesGrasperQty": null, "freeStrandDynaUsed": null, "implantsUsed": null, "intrafixUsed": null, "disposableACLQty": null, "knivesReamersUsed": null, "capitalDisposableQty": null, "coolPulseUSed": null, "materialType": null, "quantity": null, "shipToArriveDate": null, "totalShelfLife": null, "lvlOneCategories": null, "lvlTwoCategories": null, "materialStatus": null, "modProductMaterialStatus": null, "viewOnlyProduct": false, "favoriteKit": false, "nonFavoriteKit": false, "anticipatedDeliveryDate": null, "eligibleForAnticipatedDeliveryDate": false}, "updateable": false, "deliveryMode": null, "deliveryPointOfService": null, "entries": null, "configurationInfos": null, "statusSummaryMap": null, "entryGroupNumbers": null, "comments": null, "arrivalSlots": null, "supportedActions": null, "trackingNumAndUrl": null, "baseUOM": null, "salesUOM": null, "grossPrice": null, "discountsOnPrice": null, "shippingPoint": null, "totalFees": null, "freightFees": null, "handlingFee": null, "itemCategory": null, "higherLevelItemNo": null, "substitutionReason": null, "taxes": null, "dropShipFee": null, "netPrice": null, "billingDeliveryBlock": null, "minimumOrderFee": null, "priceType": null, "proNumber": null, "bolNumber": null, "carrier": null, "hsapromotion": null}, "deliveryModeChanged": null, "cartCode": null, "statusMessage": null, "entryGroupNumbers": null, "error": true}], "productsWithMaxQty": [], "invalidProductCodes": ["DK-9000"], "exceedMaxQty": false, "showQtyAdjustment": false, "showEachGTINMsg": false, "totalUnitCount": 12, "showReplacementButton": false, "jnjGTCommonFormIODataList": null}