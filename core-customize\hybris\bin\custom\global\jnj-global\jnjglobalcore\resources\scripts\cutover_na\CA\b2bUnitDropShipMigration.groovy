import de.hybris.platform.core.model.user.AddressModel
import de.hybris.platform.commerceservices.search.pagedata.PaginationData
import de.hybris.platform.commerceservices.search.pagedata.SearchPageData
import de.hybris.platform.core.model.user.AddressModel
import groovy.transform.Field
import org.apache.commons.lang.BooleanUtils
import org.apache.commons.collections.CollectionUtils
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import com.jnj.b2b.common.odata.model.*
import com.jnj.b2b.models.JnJB2BUnitModel

@Field Logger LOG = LoggerFactory.getLogger(getClass())
@Field final int DEFAULT_MAX_PAGE_SIZE = 200;
@Field final int DEFAULT_MAX_WAIT_SECONDS = 2;
@Field final String FIND_ALL_QUERY = "SELECT {p." + JnJB2BUnitModel.PK + "} FROM {" + JnJB2BUnitModel._TYPECODE + "! AS p} WHERE {p." + JnJB2BUnitModel.BUSINESSSECTOR + "} = 'MDD_NA_US'"

class ODataJnJB2BUnit {
    String billingBlock // Nullable Boolean
    String salesOrg // Nullable String
    String locName // Nullable String
    String shortCustName // Nullable String
    String name // Nullable String
    String customerGroup // Nullable String
    String shippingCondition // Nullable String
    String sourceSysId // Nullable String
    String taxId // Nullable String
    String active // Nullable Boolean
    String orderBlock // Nullable Boolean
    String centralSalesBlock // Nullable Boolean
    String deliveryBlock // Nullable Boolean
    String deletionFlag // Nullable Boolean
    String alphaName // Nullable String
    String classOfTrade // Nullable String
    String sendMethod // Nullable String
    String globalLocNo // Nullable String
    String salesRepLocNo // Nullable String
    String uid // Not Nullable String
    String businessSector
    String integrationKey // Not Nullable String
    ODataAddress contactAddress // Nullable Address
    List<ODataAddress> addresses // Nullable List of Address
    List<ODataAddress> dropShipAddresses // Nullable List of Address

    void setBillingBlock(String billingBlock) { this.billingBlock = billingBlock }
    void setSalesOrg(String salesOrg) { this.salesOrg = salesOrg }
    void setLocName(String locName) { this.locName = locName }
    void setShortCustName(String shortCustName) { this.shortCustName = shortCustName }
    void setName(String name) { this.name = name }
    void setCustomerGroup(String customerGroup) { this.customerGroup = customerGroup }
    void setShippingCondition(String shippingCondition) { this.shippingCondition = shippingCondition }
    void setSourceSysId(String sourceSysId) { this.sourceSysId = sourceSysId }
    void setTaxId(String taxId) { this.taxId = taxId }
    void setActive(String active) { this.active = active }
    void setOrderBlock(String orderBlock) { this.orderBlock = orderBlock }
    void setCentralSalesBlock(String centralSalesBlock) { this.centralSalesBlock = centralSalesBlock }
    void setDeliveryBlock(String deliveryBlock) { this.deliveryBlock = deliveryBlock }
    void setDeletionFlag(String deletionFlag) { this.deletionFlag = deletionFlag }
    void setAlphaName(String alphaName) { this.alphaName = alphaName }
    void setClassOfTrade(String classOfTrade) { this.classOfTrade = classOfTrade }
    void setSendMethod(String sendMethod) { this.sendMethod = sendMethod }
    void setGlobalLocNo(String globalLocNo) { this.globalLocNo = globalLocNo }
    void setSalesRepLocNo(String salesRepLocNo) { this.salesRepLocNo = salesRepLocNo }
    void setUid(String uid) { this.uid = uid }
    void setBusinessSector(String businessSector) { this.businessSector = businessSector }
    void setIntegrationKey(String integrationKey) { this.integrationKey = integrationKey }
    void setContactAddress(ODataAddress contactAddress) { this.contactAddress = contactAddress }
    void setAddresses(List<ODataAddress> addresses) { this.addresses = addresses }
    void setDropShipAddresses(List<ODataAddress> dropShipAddresses) { this.dropShipAddresses = dropShipAddresses }

    String getBillingBlock() { return billingBlock }
    String getSalesOrg() { return salesOrg }
    String getLocName() { return locName }
    String getShortCustName() { return shortCustName }
    String getName() { return name }
    String getCustomerGroup() { return customerGroup }
    String getShippingCondition() { return shippingCondition }
    String getSourceSysId() { return sourceSysId }
    String getTaxId() { return taxId }
    String getActive() { return active }
    String getOrderBlock() { return orderBlock }
    String getCentralSalesBlock() { return centralSalesBlock }
    String getDeliveryBlock() { return deliveryBlock }
    String getDeletionFlag() { return deletionFlag }
    String getAlphaName() { return alphaName }
    String getClassOfTrade() { return classOfTrade }
    String getSendMethod() { return sendMethod }
    String getGlobalLocNo() { return globalLocNo }
    String getSalesRepLocNo() { return salesRepLocNo }
    String getUid() { return uid }
    String getBusinessSector() { return businessSector }
    String getIntegrationKey() { return integrationKey }
    ODataAddress getContactAddress() { return contactAddress }
    List<ODataAddress> getAddresses() { return addresses }
    List<ODataAddress> getDropShipAddresses() { return dropShipAddresses }
}

class ODataAddress {
    String postalcode // Nullable String
    Boolean unloadingAddress // Nullable Boolean
    String countyName // Nullable String
    Boolean contactAddress // Nullable Boolean
    Boolean shippingAddress // Nullable Boolean
    String firstname // Nullable String
    String lastname // Nullable String
    String pobox // Nullable String
    Boolean endUserAddress // Nullable Boolean
    String building // Nullable String
    String town // Nullable String
    String district // Nullable String
    Boolean active // Nullable Boolean
    String publicKey // Nullable String
    Boolean stockPartnerAddress // Nullable Boolean
    String whatsAppNumber // Nullable String
    String sourceSysId // Nullable String
    Boolean billingAddress // Nullable Boolean
    String jnJAddressId // Nullable String
    String streetnumber // Nullable String
    String poBoxPostalCode // Nullable String
    String divisionCode // Nullable String
    String email // Nullable String
    String phone1 // Nullable String
    String cellphone // Nullable String
    String line2 // Nullable String
    String phone2 // Nullable String
    String line1 // Nullable String
    String line4 // Nullable String
    String line3 // Nullable String
    String company // Nullable String
    String streetname // Nullable String
    Boolean visibleInAddressBook // Nullable Boolean
    Boolean payFromAddress
    String department // Nullable String
    String integrationKey // Not Nullable String
    ODataCountry country // Nullable Country
    ODataTitle title // Nullable Title
    ODataRegion region // Nullable Region

    void setPostalcode(String postalcode) { this.postalcode = postalcode }
    void setUnloadingAddress(Boolean unloadingAddress) { this.unloadingAddress = unloadingAddress }
    void setCountyName(String countyName) { this.countyName = countyName }
    void setContactAddress(Boolean contactAddress) { this.contactAddress = contactAddress }
    void setShippingAddress(Boolean shippingAddress) { this.shippingAddress = shippingAddress }
    void setFirstname(String firstname) { this.firstname = firstname }
    void setLastname(String lastname) { this.lastname = lastname }
    void setPobox(String pobox) { this.pobox = pobox }
    void setEndUserAddress(Boolean endUserAddress) { this.endUserAddress = endUserAddress }
    void setBuilding(String building) { this.building = building }
    void setTown(String town) { this.town = town }
    void setDistrict(String district) { this.district = district }
    void setActive(Boolean active) { this.active = active }
    void setPublicKey(String publicKey) { this.publicKey = publicKey }
    void setStockPartnerAddress(Boolean stockPartnerAddress) { this.stockPartnerAddress = stockPartnerAddress }
    void setWhatsAppNumber(String whatsAppNumber) { this.whatsAppNumber = whatsAppNumber }
    void setSourceSysId(String sourceSysId) { this.sourceSysId = sourceSysId }
    void setBillingAddress(Boolean billingAddress) { this.billingAddress = billingAddress }
    void setJnJAddressId(String jnJAddressId) { this.jnJAddressId = jnJAddressId }
    void setStreetnumber(String streetnumber) { this.streetnumber = streetnumber }
    void setPoBoxPostalCode(String poBoxPostalCode) { this.poBoxPostalCode = poBoxPostalCode }
    void setDivisionCode(String divisionCode) { this.divisionCode = divisionCode }
    void setEmail(String email) { this.email = email }
    void setPhone1(String phone1) { this.phone1 = phone1 }
    void setCellphone(String cellphone) { this.cellphone = cellphone }
    void setLine2(String line2) { this.line2 = line2 }
    void setPhone2(String phone2) { this.phone2 = phone2 }
    void setLine1(String line1) { this.line1 = line1 }
    void setLine4(String line4) { this.line4 = line4 }
    void setLine3(String line3) { this.line3 = line3 }
    void setCompany(String company) { this.company = company }
    void setStreetname(String streetname) { this.streetname = streetname }
    void setVisibleInAddressBook(Boolean visibleInAddressBook) { this.visibleInAddressBook = visibleInAddressBook }
    void setPayFromAddress(Boolean payFromAddress) { this.payFromAddress = payFromAddress }
    void setDepartment(String department) { this.department = department }
    void setIntegrationKey(String integrationKey) { this.integrationKey = integrationKey }
    void setCountry(ODataCountry country) { this.country = country }
    void setTitle(ODataTitle title) { this.title = title }
    void setRegion(ODataRegion region) { this.region = region }

    String getPostalcode() { return postalcode }
    Boolean getUnloadingAddress() { return unloadingAddress }
    String getCountyName() { return countyName }
    Boolean getContactAddress() { return contactAddress }
    Boolean getShippingAddress() { return shippingAddress }
    String getFirstname() { return firstname }
    String getLastname() { return lastname }
    String getPobox() { return pobox }
    Boolean getEndUserAddress() { return endUserAddress }
    String getBuilding() { return building }
    String getTown() { return town }
    String getDistrict() { return district }
    Boolean getActive() { return active }
    String getPublicKey() { return publicKey }
    Boolean getStockPartnerAddress() { return stockPartnerAddress }
    String getWhatsAppNumber() { return whatsAppNumber }
    String getSourceSysId() { return sourceSysId }
    Boolean getBillingAddress() { return billingAddress }
    String getJnJAddressId() { return jnJAddressId }
    String getStreetnumber() { return streetnumber }
    String getPoBoxPostalCode() { return poBoxPostalCode }
    String getDivisionCode() { return divisionCode }
    String getEmail() { return email }
    String getPhone1() { return phone1 }
    String getCellphone() { return cellphone }
    String getLine2() { return line2 }
    String getPhone2() { return phone2 }
    String getLine1() { return line1 }
    String getLine4() { return line4 }
    String getLine3() { return line3 }
    String getCompany() { return company }
    String getStreetname() { return streetname }
    Boolean getVisibleInAddressBook() { return visibleInAddressBook }
    Boolean getPayFromAddress() { return payFromAddress }
    String getDepartment() { return department }
    String getIntegrationKey() { return integrationKey }
    ODataCountry getCountry() { return country }
    ODataTitle getTitle() { return title }
    ODataRegion getRegion() { return region }
}

class ODataJnJOdataB2BUnit {
    String id // Not Nullable String
    String integrationKey // Not Nullable String
    List<ODataJnJB2BUnit> b2bunits // Not Nullable List of JnJB2BUnit

    void setId(String id) { this.id = id }
    void setIntegrationKey(String integrationKey) { this.integrationKey = integrationKey }
    void setB2bunits(List<ODataJnJB2BUnit> b2bunits) { this.b2bunits = b2bunits }

    String getId() { return id }
    String getIntegrationKey() { return integrationKey }
    List<ODataJnJB2BUnit> getB2bunits() { return b2bunits }
}

class ODataCountry {
    String isocode // Not Nullable String
    String integrationKey // Not Nullable String

    void setIsocode(String isocode) { this.isocode = isocode }
    void setIntegrationKey(String integrationKey) { this.integrationKey = integrationKey }

    String getIsocode() { return isocode }
    String getIntegrationKey() { return integrationKey }
}

class ODataRegion {
    String isocode // Not Nullable String
    String integrationKey // Not Nullable String
    ODataCountry country // Nullable Country

    void setIsocode(String isocode) { this.isocode = isocode }
    void setIntegrationKey(String integrationKey) { this.integrationKey = integrationKey }
    void setCountry(ODataCountry country) { this.country = country }

    String getIsocode() { return isocode }
    String getIntegrationKey() { return integrationKey }
    ODataCountry getCountry() { return country }
}

class ODataTitle {
    String code // Not Nullable String
    String integrationKey // Not Nullable String

    void setCode(String code) { this.code = code }
    void setIntegrationKey(String integrationKey) { this.integrationKey = integrationKey }

    String getCode() { return code }
    String getIntegrationKey() { return integrationKey }
}

def ODataJnJB2BUnit dataMapping(JnJB2BUnitModel jb2BUnitModel) {

    // Only process dropShipAddresses
    if(CollectionUtils.isNotEmpty(jb2BUnitModel.getDropShipAddresses())) {
         ODataJnJB2BUnit dataMainJnJB2BUnit = new ODataJnJB2BUnit()
        // Only set UID
        dataMainJnJB2BUnit.setUid(jb2BUnitModel.getUid())
        List<ODataAddress> dropShipAddresses = new ArrayList<>()
        for(AddressModel address: jb2BUnitModel.getDropShipAddresses()) {
            ODataAddress oDataAddress = dataAddressMapping(address)
            dropShipAddresses.add(oDataAddress)
        }
        dataMainJnJB2BUnit.setDropShipAddresses(dropShipAddresses)
        return dataMainJnJB2BUnit
    }
    return null
}


def ODataAddress dataAddressMapping(AddressModel address){
    ODataAddress oDataAddress = new ODataAddress();
    return populateAddressData(oDataAddress, address);
}

def ODataAddress populateAddressData(ODataAddress oDataAddress, AddressModel address) {
    oDataAddress.setJnJAddressId(address.getJnJAddressId());
    if(address.getTitle() != null){
        ODataTitle dataTitle = new ODataTitle();
        dataTitle.setCode(address.getTitle().getCode());
        oDataAddress.setTitle(dataTitle);
    }
    //FLAGS
    oDataAddress.setActive(BooleanUtils.isTrue(address.isActive()));
    oDataAddress.setShippingAddress(BooleanUtils.isTrue(address.getShippingAddress()));
    oDataAddress.setVisibleInAddressBook(BooleanUtils.isTrue(address.getVisibleInAddressBook()));
    oDataAddress.setBillingAddress(BooleanUtils.isTrue(address.getBillingAddress()));
    oDataAddress.setUnloadingAddress(BooleanUtils.isTrue(address.getUnloadingAddress()));
    oDataAddress.setContactAddress(BooleanUtils.isTrue(address.getContactAddress()));
    oDataAddress.setPayFromAddress(BooleanUtils.isTrue(address.getPayFromAddress()));

    if(address.getCountry() != null){
        ODataCountry dataCountry = new ODataCountry();
        dataCountry.setIsocode(address.getCountry().getIsocode());
        oDataAddress.setCountry(dataCountry);
    }
    //Address
    oDataAddress.setEmail(address.getEmail());
    oDataAddress.setBuilding(address.getBuilding());
    oDataAddress.setCellphone(address.getCellphone());
    oDataAddress.setCompany(address.getCompany());
    oDataAddress.setFirstname(address.getFirstname());
    oDataAddress.setLastname(address.getLastname());
    oDataAddress.setCompany(address.getCompany());
    oDataAddress.setCountyName(address.getCountyName());
    oDataAddress.setDepartment(address.getDepartment());
    oDataAddress.setDistrict(address.getDistrict());

    //oDataAddress.setDivisionCode("10")
    //oDataAddress.setPublicKey(address.getJnJAddressId())
    oDataAddress.setEndUserAddress(BooleanUtils.isTrue(address.getEndUserAddress()));
    oDataAddress.setLine1(address.getLine1());
    oDataAddress.setLine2(address.getLine2());
    oDataAddress.setLine3(address.getLine3());
    oDataAddress.setLine4(address.getLine4());
    oDataAddress.setPhone1(address.getPhone1());
    oDataAddress.setPhone2(address.getPhone2());
    oDataAddress.setPobox(address.getPobox());
    oDataAddress.setPoBoxPostalCode(address.getPoBoxPostalCode());
    oDataAddress.setPostalcode(address.getPostalcode());
    oDataAddress.setPublicKey(address.getPublicKey());
    oDataAddress.setSourceSysId(address.getSourceSysId());
    oDataAddress.setStockPartnerAddress(BooleanUtils.isTrue(address.getStockPartnerAddress()));
    oDataAddress.setStreetname(address.getStreetname());
    oDataAddress.setStreetnumber(address.getStreetnumber());
    oDataAddress.setTown(address.getTown());
    // oDataAddress.setWhatsAppNumber(address.getWhatsAppNumber());

    if(address.getRegion() != null) {
        ODataRegion dataRegion = new ODataRegion();
        dataRegion.setIsocode(address.getRegion().getIsocode());
        if (address.getRegion().getCountry() != null) {
            ODataCountry dataCountry = new ODataCountry();
            dataCountry.setIsocode(address.getCountry().getIsocode());
            dataRegion.setCountry(dataCountry);
        }
        oDataAddress.setRegion(dataRegion);
    }
    //resettting flags with default values
    oDataAddress.setBillingAddress(false);
    oDataAddress.setShippingAddress(false);
    oDataAddress.setContactAddress(false);
    oDataAddress.setUnloadingAddress(false);
    oDataAddress.setPayFromAddress(false);

    return oDataAddress;
}

def SearchPageData<JnJB2BUnitModel> listAllB2BUnits(PaginationData paginationData) {
    FlexibleSearchQuery searchQuery = new FlexibleSearchQuery(FIND_ALL_QUERY)
    searchQuery.setStart(paginationData.getCurrentPage() * paginationData.getPageSize())
    searchQuery.setCount(paginationData.getPageSize())
    
    SearchResult<JnJB2BUnitModel> searchResult = flexibleSearchService.search(searchQuery)
    
    SearchPageData<JnJB2BUnitModel> searchPageData = new SearchPageData<>()
    searchPageData.setResults(searchResult.getResult())
    searchPageData.setPagination(createPagination(paginationData, searchResult))
    
    return searchPageData
}

def PaginationData createPagination(PaginationData pageableData, SearchResult<?> searchResult) {
    PaginationData paginationData = new PaginationData()
    paginationData.setPageSize(pageableData.getPageSize())
    paginationData.setTotalNumberOfResults(searchResult.getTotalCount())
    paginationData.setNumberOfPages((int) Math.ceil(((double) paginationData.getTotalNumberOfResults()) / paginationData.getPageSize()))
    paginationData.setCurrentPage(pageableData.getCurrentPage())
    return paginationData
}

def processPages(PaginationData paginationData, List<JnJB2BUnitModel> results) {
    LOG.info("Processing page {} of {}", paginationData.getCurrentPage(), paginationData.getNumberOfPages())
    
    List<ODataJnJB2BUnit> b2bUnits = new ArrayList<>()
    
    for(JnJB2BUnitModel jb2BUnitModel : results) {
        if(CollectionUtils.isNotEmpty(jb2BUnitModel.getDropShipAddresses())) {
            ODataJnJB2BUnit resultedB2BUnit = dataMapping(jb2BUnitModel)
            if(resultedB2BUnit == null) {
                continue
            }
            b2bUnits.add(resultedB2BUnit);
        }
    }
    def payload
    try {
    if(!b2bUnits.isEmpty()) {
        ODataJnJOdataB2BUnit dataB2BUnitRequest = new ODataJnJOdataB2BUnit()
        dataB2BUnitRequest.setId("B2BUnit-dropship-migration-batch:" + paginationData.getCurrentPage() + "-" + System.currentTimeMillis())
        dataB2BUnitRequest.setB2bunits(b2bUnits)
        
       payload = new JsonGenerator.Options()
                .excludeNulls()
                .build()
                .toJson(dataB2BUnitRequest)
                
        createODataOperation(TARGET_ODATA_URL + "/" + ODATA_CONTEXT + "/" + ODATA_CONTEXT_TYPE, payload)
    }
     } catch(Exception e) {
        def errorCode = getErrorCode(e)
        LOG.error("Error Processing B2BUnit Data CurrentErrorPage: {} with error code: {}",
                paginationData.getCurrentPage(), errorCode, e)
    }
}

def String getErrorCode(Exception e) {
    def message = e.message ?: e.cause?.message ?: ""
    // Add more specific error codes
    switch (true) {
        case message.contains("503"): return "503"
        case message.contains("400"): return "400"
        case message.contains("401"): return "401" // Unauthorized
        case message.contains("404"): return "404" // Not Found
        case message.contains("timeout"): return "TIMEOUT"
        case message.contains("connection refused"): return "CONNECTION_REFUSED"
        default: return "UNKNOWN"
    }
}

// Main execution
LOG.info("Starting B2B Unit DropShip Address Migration")
def paginationData = new PaginationData()
paginationData.setPageSize(DEFAULT_PAGE_SIZE)
paginationData.setCurrentPage(0)

do {
    def searchPageData = listAllB2BUnits(paginationData)
    if(CollectionUtils.isNotEmpty(searchPageData.getResults())) {
        processPages(searchPageData.getPagination(), searchPageData.getResults())
    }
    paginationData = searchPageData.getPagination()
    paginationData.setCurrentPage(paginationData.getCurrentPage() + 1)
    LOG.info("B2B Unit DropShip Address Migration - Execution wait for {} seconds", DEFAULT_MAX_WAIT_SECONDS)
    TimeUnit.SECONDS.sleep(DEFAULT_MAX_WAIT_SECONDS)
} while(paginationData.getCurrentPage() < paginationData.getNumberOfPages())

LOG.info("B2B Unit DropShip Address Migration completed")

