import com.jnj.core.jalo.JnJB2BUnit
import com.jnj.core.model.JnJB2BUnitModel
import com.jnj.core.model.JnJGTPriceRowModel
import com.jnj.core.model.JnJProductModel
import com.jnj.core.model.JnjGTSalesOrgCustomerModel
import com.jnj.core.model.JnjGTVariantProductModel

import de.hybris.platform.b2b.model.B2BUnitModel
import de.hybris.platform.catalog.CatalogVersionService
import de.hybris.platform.catalog.model.CatalogVersionModel
import de.hybris.platform.category.model.CategoryModel
import de.hybris.platform.core.model.c2l.CountryModel
import de.hybris.platform.core.model.media.MediaContainerModel
import de.hybris.platform.core.model.media.MediaModel
import de.hybris.platform.core.model.product.ProductModel
import de.hybris.platform.core.model.user.AddressModel
import de.hybris.platform.core.servicelayer.data.PaginationData
import de.hybris.platform.core.servicelayer.data.SearchPageData
import de.hybris.platform.servicelayer.search.FlexibleSearchQuery
import de.hybris.platform.servicelayer.search.paginated.PaginatedFlexibleSearchParameter
import de.hybris.platform.servicelayer.search.paginated.PaginatedFlexibleSearchService

import groovy.json.JsonBuilder
import groovy.json.JsonGenerator
import groovy.json.JsonOutput
import groovy.json.JsonSlurper
import groovy.transform.Field

import java.text.DateFormat
import java.text.SimpleDateFormat
import java.util.concurrent.TimeUnit

import org.apache.commons.collections.CollectionUtils
import org.apache.commons.collections.SetUtils
import org.apache.commons.lang.BooleanUtils
import org.apache.commons.lang3.StringUtils
import org.apache.commons.lang3.time.DateUtils
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.http.HttpEntity
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpMethod
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity
import org.springframework.util.LinkedMultiValueMap
import org.springframework.util.MultiValueMap
import org.springframework.web.client.RestTemplate

@Field final String ACCESS_TOKEN_KEY = 'access_token'
@Field final String TARGET_ODATA_URL = 'https://api.c4fai9-johnsonan4-s2-public.model-t.cc.commerce.ondemand.com/odata2webservices'
@Field final String TARGET_AUTHORIZATION_URL = "https://api.c4fai9-johnsonan4-s2-public.model-t.cc.commerce.ondemand.com/authorizationserver/oauth/token"
@Field final String ODATA_CONTEXT = 'JnJInboundMigrationB2BUnit'
@Field final String ODATA_CONTEXT_TYPE = 'JnJOdataB2BUnits'
@Field final String CATALOG_ID = 'mddNAProdCatalog'
@Field final String CATALOG_VERSION = 'Staged'
@Field final int DEFAULT_MAX_PAGE_SIZE = 100;
@Field final int DEFAULT_MAX_WAIT_SECONDS = 10;
@Field final String MODIFIED_DELTA_DATE = '2017-05-15'
@Field final String MODIFIED_DELTA_DATE_TO= '2025-04-15'
@Field final String TARGET_PRODUCT_CATALOG_ID = 'jnjUSProductCatalog'
@Field final String SALES_ORG = 'US01';
@Field final String DISTRIBUTION_CHANNEL = '10';

@Field final Logger LOG = LoggerFactory.getLogger(Logger.class);
@Field final CatalogVersionService catalogVersionService = spring.getBean("catalogVersionService");

@Field final PaginatedFlexibleSearchService paginatedFlexibleSearchService = spring.getBean("paginatedFlexibleSearchService");

@Field final String FIND_ALL_QUERY = " SELECT " +
        " {p." + JnJB2BUnitModel.PK + "} " +
        " FROM" + " {" + JnJB2BUnitModel._TYPECODE + "! AS p} " +
        " WHERE " +
        " {p." + JnJB2BUnitModel.BUSINESSSECTOR + "} = 'MDD_NA_US' " +
//        "AND {p."+ JnJB2BUnitModel.INDICATOR + "} = 'International_Affiliate' " +
        "order by {p.uid}";


@Field long scriptStartTime
@Field long initialProcessingEndTime
@Field long retryEndTime
@Field int totalProcessedPages = 0
@Field int totalRetryPages = 0

def String getAuthenticationToken(){
    // RestTemplate
    RestTemplate restTemplate = new RestTemplate()

    // URL REST
    String url = TARGET_AUTHORIZATION_URL;
    HttpHeaders headers = new HttpHeaders();
    headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

    MultiValueMap<String, String> map = new LinkedMultiValueMap<>();
    map.add("client_id","mulesoft");
    map.add("client_secret","mulesoft@123");
    map.add("grant_type","password");
    map.add("username","emeaintegrationuser");
    map.add("password","emeaintegration@123");

    HttpEntity<MultiValueMap<String, String>> entity = new HttpEntity<>(map, headers);

    ResponseEntity<String> response =
            restTemplate.exchange(url,
                    HttpMethod.POST,
                    entity,
                    String.class);


    if(response.getStatusCode().is2xxSuccessful()){
        def parser = new JsonSlurper()
        def json = parser.parseText(response.getBody());
        return json.getAt(ACCESS_TOKEN_KEY);
    }
    return StringUtils.EMPTY;
}

//Create Single Data by calling SAP Commerce OData Layer For JnJ
def String createODataOperation(String targetUrl, String body){
    RestTemplate restTemplate = new RestTemplate();
    // Header Settings
    HttpHeaders headers = new HttpHeaders()
    headers.set("Content-Type", "application/json")
    headers.set("Authorization", "Bearer " + getAuthenticationToken());
    def json = new JsonSlurper().parseText(body);
    HttpEntity<String> entity = new HttpEntity<>(json, headers);
    // Call  POST METHOD
    ResponseEntity<String> response = restTemplate.exchange(targetUrl, HttpMethod.POST, entity, String.class)

    return response.getBody();
}

def String createBatchODataOperation(String targetUrl, String body){
    RestTemplate restTemplate = new RestTemplate();
    // Header Settings
    def bodyBatch = "--batch\n" +
            "Content-Type: multipart/mixed; boundary=changeset\n" +
            "\n" +
            "--changeset\n" +
            "Content-Type: application/http\n" +
            "Content-Transfer-Encoding: binary\n" +
            "\n" +
            "POST JnJProducts HTTP/1.1\n" +
            "Accept: application/json\n" +
            "Content-Type: application/json \n" + body + "\n" +
            "--changeset--\n" +
            "--batch--"
    try {
        HttpHeaders headers = new HttpHeaders()
        headers.set("Content-Type", "multipart/mixed;boundary=batch")
        headers.set("Authorization", "Bearer " + getAuthenticationToken());
        //def json = new JsonSlurper().parseText(bodyBatch);
        HttpEntity<String> entity = new HttpEntity<>(bodyBatch, headers);
        // Call  POST METHOD
        ResponseEntity<String> response = restTemplate.exchange(targetUrl, HttpMethod.POST, entity, String.class)
        return response.getBody();
    }catch(Exception e){
        LOG.error("Error Batch :",e)
        return StringUtils.EMPTY
    }
}

def SearchPageData<JnJB2BUnitModel> listAll(String createdDateTime, SearchPageData searchPageData) {
    try {
        final Map<String, Object> params = new HashMap<String, Object>();
        params.put(JnJB2BUnitModel.MODIFIEDTIME, getCalendardDate(DateUtils.parseDate(MODIFIED_DELTA_DATE, new String[]{"yyyy-MM-dd"}), true));
        params.put("currentDate", getCalendardDate(DateUtils.parseDate(MODIFIED_DELTA_DATE_TO, new String[]{"yyyy-MM-dd"}), false));

        String query = FIND_ALL_QUERY;

        FlexibleSearchQuery searchQuery = new FlexibleSearchQuery(query, params);
        PaginatedFlexibleSearchParameter parameter = new PaginatedFlexibleSearchParameter();
        parameter.setFlexibleSearchQuery(searchQuery);
        parameter.setSearchPageData(searchPageData);

        return paginatedFlexibleSearchService.search(parameter);
    }catch(Exception e){
        LOG.error("list all error", e);
        throw e;
    }
}


def Date getCalendardDate(Date date, boolean createdTime) {
    Calendar cal = Calendar.getInstance();
    cal.setTime(date);
    if(createdTime){
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);
    }else{
        cal.set(Calendar.HOUR_OF_DAY, 23);
        cal.set(Calendar.MINUTE, 59);
        cal.set(Calendar.SECOND, 59);
        cal.set(Calendar.MILLISECOND, 999);
    }

    return cal.getTime();
}

def String getODataConvertedDate(Date dateToConvert) {
    if(dateToConvert == null){
        return null;
    }
    return "/Date("+dateToConvert.getTime()+")/";
}

def SearchPageData getSearchPageData() {
    SearchPageData searchPageData = new SearchPageData();
    searchPageData.setPagination(getPagination());
    return searchPageData;
}

def PaginationData getPagination() {
    PaginationData paginationData = new PaginationData();
    paginationData.setCurrentPage(0);
    paginationData.setPageSize(DEFAULT_MAX_PAGE_SIZE);
    paginationData.setNeedsTotal(true);
    return  paginationData;
}

def CatalogVersionModel getCatalogVersion(){
    def CatalogVersionModel catalogVersionModel =  catalogVersionService.getCatalogVersion(CATALOG_ID,CATALOG_VERSION);
    return catalogVersionModel;
}


def ODataAddress dataAddressMapping(AddressModel address){
    ODataAddress oDataAddress = new ODataAddress();
    return populateAddressData(oDataAddress, address);
}

def ODataAddress populateAddressData(ODataAddress oDataAddress, AddressModel address) {
    oDataAddress.setJnJAddressId(address.getJnJAddressId());
    if(address.getTitle() != null){
        ODataTitle dataTitle = new ODataTitle();
        dataTitle.setCode(address.getTitle().getCode());
        oDataAddress.setTitle(dataTitle);
    }
    //FLAGS
    oDataAddress.setActive(BooleanUtils.isTrue(address.isActive()));
    oDataAddress.setShippingAddress(BooleanUtils.isTrue(address.getShippingAddress()));
    oDataAddress.setVisibleInAddressBook(BooleanUtils.isTrue(address.getVisibleInAddressBook()));
    oDataAddress.setBillingAddress(BooleanUtils.isTrue(address.getBillingAddress()));
    oDataAddress.setUnloadingAddress(BooleanUtils.isTrue(address.getUnloadingAddress()));
    oDataAddress.setContactAddress(BooleanUtils.isTrue(address.getContactAddress()));
    oDataAddress.setPayFromAddress(BooleanUtils.isTrue(address.getPayFromAddress()));

    if(address.getCountry() != null){
        ODataCountry dataCountry = new ODataCountry();
        dataCountry.setIsocode(address.getCountry().getIsocode());
        oDataAddress.setCountry(dataCountry);
    }
    //Address
    oDataAddress.setEmail(address.getEmail());
    oDataAddress.setBuilding(address.getBuilding());
    oDataAddress.setCellphone(address.getCellphone());
    oDataAddress.setCompany(address.getCompany());
    oDataAddress.setFirstname(address.getFirstname());
    oDataAddress.setLastname(address.getLastname());
    oDataAddress.setCompany(address.getCompany());
    oDataAddress.setCountyName(address.getCountyName());
    oDataAddress.setDepartment(address.getDepartment());
    oDataAddress.setDistrict(address.getDistrict());

    //oDataAddress.setDivisionCode("10")
    //oDataAddress.setPublicKey(address.getJnJAddressId())
    oDataAddress.setEndUserAddress(BooleanUtils.isTrue(address.getEndUserAddress()));
    oDataAddress.setLine1(address.getLine1());
    oDataAddress.setLine2(address.getLine2());
    oDataAddress.setLine3(address.getLine3());
    oDataAddress.setLine4(address.getLine4());
    oDataAddress.setPhone1(address.getPhone1());
    oDataAddress.setPhone2(address.getPhone2());
    oDataAddress.setPobox(address.getPobox());
    oDataAddress.setPoBoxPostalCode(address.getPoBoxPostalCode());
    oDataAddress.setPostalcode(address.getPostalcode());
    oDataAddress.setPublicKey(address.getPublicKey());
    oDataAddress.setSourceSysId(address.getSourceSysId());
    oDataAddress.setStockPartnerAddress(BooleanUtils.isTrue(address.getStockPartnerAddress()));
    oDataAddress.setStreetname(address.getStreetname());
    oDataAddress.setStreetnumber(address.getStreetnumber());
    oDataAddress.setTown(address.getTown());
    // oDataAddress.setWhatsAppNumber(address.getWhatsAppNumber());

    if(address.getRegion() != null) {
        ODataRegion dataRegion = new ODataRegion();
        dataRegion.setIsocode(address.getRegion().getIsocode());
        if (address.getRegion().getCountry() != null) {
            ODataCountry dataCountry = new ODataCountry();
            dataCountry.setIsocode(address.getCountry().getIsocode());
            dataRegion.setCountry(dataCountry);
        }
        oDataAddress.setRegion(dataRegion);
    }
    //resettting flags with default values
    oDataAddress.setBillingAddress(false);
    oDataAddress.setShippingAddress(false);
    oDataAddress.setContactAddress(false);
    oDataAddress.setUnloadingAddress(false);
    oDataAddress.setPayFromAddress(false);

    return oDataAddress;
}

def List<ODataJnJB2BUnit> dataMapping(JnJB2BUnitModel jb2BUnitModel){

    List<ODataJnJB2BUnit> dataJnJB2BUnitRequestListData = new ArrayList<>();
    ODataJnJB2BUnit dataMainJnJB2BUnit = new ODataJnJB2BUnit();
    //TODO: Remove the test suffix
    dataMainJnJB2BUnit.setUid(jb2BUnitModel.getUid());
    dataMainJnJB2BUnit.setName(jb2BUnitModel.getName());
    dataMainJnJB2BUnit.setSalesOrg(SALES_ORG);
    dataMainJnJB2BUnit.setBusinessSector(jb2BUnitModel.getBusinessSector());
    dataMainJnJB2BUnit.setIndicator(jb2BUnitModel.getIndicator());

    //Setting country
    if(jb2BUnitModel.getCountry() != null){
        ODataCountry dataCountry = new ODataCountry();
        dataCountry.setIsocode(jb2BUnitModel.getCountry().getIsocode());
        dataMainJnJB2BUnit.setCountry(dataCountry);
    }

    if(CollectionUtils.isNotEmpty(jb2BUnitModel.getAddresses())){
        List<AddressModel> addresses = jb2BUnitModel.getAddresses();
        List<ODataAddress>  oDataAddressList = new ArrayList<>();
        for(AddressModel address: addresses){
            //Only Address from current b2b unit is configured
            //TODO: Need to revisit it to understand if going to create the b2b unit for
            String uid_jnj = address.getJnJAddressId();
            if(!dataMainJnJB2BUnit.getUid().equalsIgnoreCase(uid_jnj)){
                continue;
            }
            ODataAddress oDataAddress = dataAddressMapping(address);
            oDataAddressList.add(oDataAddress);
            dataMainJnJB2BUnit.setContactAddress(oDataAddress);
            break;
        }
        dataMainJnJB2BUnit.setAddresses(oDataAddressList);
    }

    List<ODataGroup> dataGroupList = new ArrayList<>();
    List<ODataSalesOrgCustomer> salesOrgCustomers = new ArrayList<>();
    if(CollectionUtils.isNotEmpty(jb2BUnitModel.getSalesOrgCustomers())){
        for(JnjGTSalesOrgCustomerModel jnjGTSalesOrgCustomerModel:jb2BUnitModel.getSalesOrgCustomers()){
            // Create ODataSalesOrgCustomer
            ODataSalesOrgCustomer salesOrgCustomer = new ODataSalesOrgCustomer();
            salesOrgCustomer.setCode(jb2BUnitModel.getUid()+"|"+jnjGTSalesOrgCustomerModel.getDivision());
            salesOrgCustomer.setPriceListId(jnjGTSalesOrgCustomerModel.getPriceListId());
            salesOrgCustomer.setSalesOrg(SALES_ORG);
            salesOrgCustomer.setDistributionChannel(DISTRIBUTION_CHANNEL);
            salesOrgCustomer.setSourceSysId(jnjGTSalesOrgCustomerModel.getSourceSysId());
            salesOrgCustomer.setDivision(jnjGTSalesOrgCustomerModel.getDivision());
            salesOrgCustomer.setBlockCode(jnjGTSalesOrgCustomerModel.getBlockCode());
            salesOrgCustomer.setBusinessSector(jnjGTSalesOrgCustomerModel.getBusinessSector());
            salesOrgCustomer.setCustomerGroup(jnjGTSalesOrgCustomerModel.getCustomerGroup());

            if(jnjGTSalesOrgCustomerModel.getUserPriceGroup() != null) {
                ODataUserPriceGroup userPriceGroup = new ODataUserPriceGroup();
                userPriceGroup.setCode(jnjGTSalesOrgCustomerModel.getUserPriceGroup().getCode());
                salesOrgCustomer.setUserPriceGroup(userPriceGroup);
            }

            salesOrgCustomers.add(salesOrgCustomer);

        }
    }

    // Create bridge B2B unit
    ODataJnJB2BUnit oDataJnJB2BUnitBridge = mapBridgeB2BUnit(jb2BUnitModel);
    ODataGroup dataGroup = new ODataGroup();
    dataGroup.setUid(oDataJnJB2BUnitBridge.getUid());
    dataGroupList.add(dataGroup);
    dataJnJB2BUnitRequestListData.add(oDataJnJB2BUnitBridge);

    // Set salesOrgCustomers to the main B2B unit
    dataMainJnJB2BUnit.setSalesOrgCustomers(salesOrgCustomers);
    /*
    // Add dropShipAddresses to the main B2B unit
    if(CollectionUtils.isNotEmpty(jb2BUnitModel.getDropShipAddresses())) {
        List<ODataAddress> dropShipAddresses = new ArrayList<>();
        List<ODataAddress>  oDataAddressList = dataMainJnJB2BUnit.getAddresses();
        for(AddressModel address: jb2BUnitModel.getDropShipAddresses()) {
            ODataAddress oDataAddress = dataAddressMapping(address);
            oDataAddressList.add(oDataAddress);
            dropShipAddresses.add(oDataAddress);
        }
        //dataMainJnJB2BUnit.setAddresses(oDataAddressList);
        dataMainJnJB2BUnit.setDropShipAddresses(dropShipAddresses);
    }
    */

    dataMainJnJB2BUnit.setGroups(dataGroupList);
    dataJnJB2BUnitRequestListData.add(dataMainJnJB2BUnit);

    return dataJnJB2BUnitRequestListData;
}

def ODataJnJB2BUnit mapBridgeB2BUnit(JnJB2BUnitModel baseB2Unit){
    ODataJnJB2BUnit dataBridgeB2BUnit = new ODataJnJB2BUnit();
    dataBridgeB2BUnit.setUid(baseB2Unit.getUid() + "|" + SALES_ORG + "|" + DISTRIBUTION_CHANNEL+"|00");
    dataBridgeB2BUnit.setName(dataBridgeB2BUnit.getUid());
    List<AddressModel> addressModelList = baseB2Unit.getAddresses();

    // Create a Set of JnJAddressIds from dropShipAddresses for efficient lookup
    Set<String> dropShipAddressIds = new HashSet<>();
    if(CollectionUtils.isNotEmpty(baseB2Unit.getDropShipAddresses())) {
        baseB2Unit.getDropShipAddresses().each { addr ->
            if(addr.getJnJAddressId() != null) {
                dropShipAddressIds.add(addr.getJnJAddressId())
            }
        }
    }

    if(CollectionUtils.isNotEmpty(addressModelList)){
        // Use a Map to avoid duplicates based on accountId and partnerFunctionCode
        Map<String, ODataJnJPartnerFunction> partnerFunctionMap = new HashMap<>();

        for(AddressModel addressModel: addressModelList){
            String accountId = addressModel.getJnJAddressId();

            // Skip if this address is in dropShipAddresses
            if(accountId != null && dropShipAddressIds.contains(accountId)) {
                continue;
            }

            if(BooleanUtils.isTrue(addressModel.getShippingAddress())){
                // Add SH partner function
                String shKey = accountId + "_SH";
                if(!partnerFunctionMap.containsKey(shKey)) {
                    partnerFunctionMap.put(shKey, mapPartnerFunction(addressModel, "SH"));
                }

                // Add SP partner function if it's also a contact address and its main b2bunit id
                if(baseB2Unit.getUid().equalsIgnoreCase(accountId) && BooleanUtils.isTrue(addressModel.getContactAddress())){
                    String spKey = accountId + "_SP";
                    if(!partnerFunctionMap.containsKey(spKey)) {
                        partnerFunctionMap.put(spKey, mapPartnerFunction(addressModel, "SP"));
                    }
                }
            }

            if(BooleanUtils.isTrue(addressModel.getBillingAddress())){
                String bpKey = accountId + "_BP";
                if(!partnerFunctionMap.containsKey(bpKey)) {
                    partnerFunctionMap.put(bpKey, mapPartnerFunction(addressModel, "BP"));
                }
            }

            if(BooleanUtils.isTrue(addressModel.getPayFromAddress())){
                String pyKey = accountId + "_PY";
                if(!partnerFunctionMap.containsKey(pyKey)) {
                    partnerFunctionMap.put(pyKey, mapPartnerFunction(addressModel, "PY"));
                }
            }
        }

        // Convert the Map values to a List
        List<ODataJnJPartnerFunction> dataJnJPartnerFunctions = new ArrayList<>(partnerFunctionMap.values());
        dataBridgeB2BUnit.setPartnerFunction(dataJnJPartnerFunctions);
    }

    return dataBridgeB2BUnit;
}

def ODataJnJPartnerFunction mapPartnerFunction(AddressModel address, String partnerFunctionCode){
    ODataJnJPartnerFunction dataJnJPartnerFunction = new ODataJnJPartnerFunction();
    dataJnJPartnerFunction.setSalesOrg(SALES_ORG);
    dataJnJPartnerFunction.setDistChannel(DISTRIBUTION_CHANNEL);
    dataJnJPartnerFunction.setDivison("00");
    ODataJnJPartnerFunctionCode dataJnJPartnerFunctionCode = new ODataJnJPartnerFunctionCode();
    dataJnJPartnerFunctionCode.setCode(partnerFunctionCode);
    dataJnJPartnerFunction.setPartnerFunctionCode(dataJnJPartnerFunctionCode);
    dataJnJPartnerFunction.setAccountId(address.getJnJAddressId());
    return dataJnJPartnerFunction;
}

def processPages(PaginationData paginationData, List<JnJB2BUnitModel> results) {
    LOG.info("processPages started Index {}", paginationData.getCurrentPage())
    def payload
    try {
        List<ODataJnJB2BUnit> b2bUnits = new ArrayList<>();

        for(JnJB2BUnitModel jb2BUnitModel:results){
            if(jb2BUnitModel.getUid().equalsIgnoreCase("********")){
                continue;
            }
            List<ODataJnJB2BUnit> resultedB2BUnits = dataMapping(jb2BUnitModel);
            resultedB2BUnits.stream().forEach(oDataB2BUnit->{
                b2bUnits.add(oDataB2BUnit);
            })
        }
        ODataJnJOdataB2BUnit dataB2BUnitRequest  = new ODataJnJOdataB2BUnit();
        dataB2BUnitRequest.setId("B2BUnit-migration-batch:"+ paginationData.getCurrentPage() + "-" + System.currentTimeMillis());
        dataB2BUnitRequest.setB2bunits(b2bUnits);
        def generator = new JsonGenerator.Options()
                .excludeNulls()
                .build();
        payload =  generator.toJson(dataB2BUnitRequest);
        def apiResult = createODataOperation(TARGET_ODATA_URL + "/" + ODATA_CONTEXT + "/" + ODATA_CONTEXT_TYPE, payload);
        LOG.info("processPages ended Index {}", paginationData.getCurrentPage())
    } catch(Exception e) {
        def errorCode = getErrorCode(e)
        LOG.error("Error Processing B2BUnit Data CurrentErrorPage: {} with error code: {}",
                paginationData.getCurrentPage(), errorCode, e)
        handleFailedPage(paginationData.getCurrentPage(), errorCode, payload)
    }
}

def String getErrorCode(Exception e) {
    def message = e.message ?: e.cause?.message ?: ""
    // Add more specific error codes
    switch (true) {
        case message.contains("503"): return "503"
        case message.contains("400"): return "400"
        case message.contains("401"): return "401" // Unauthorized
        case message.contains("404"): return "404" // Not Found
        case message.contains("timeout"): return "TIMEOUT"
        case message.contains("connection refused"): return "CONNECTION_REFUSED"
        default: return "UNKNOWN"
    }
}

def void handleFailedPage(Integer pageNum, String errorCode, String payload) {
    if (!binding.hasVariable('failedPages')) {
        binding.setVariable('failedPages', [:])
    }

    if (!binding.hasVariable('failedPagesData')) {
        binding.setVariable('failedPagesData', [:])
    }

    def pageKey = "${pageNum}_${errorCode}"

    // Track retry count for both 503 and UNKNOWN errors
    if (errorCode == "503" || errorCode == "UNKNOWN") {
        def retryCount = failedPages[pageKey] ?: 0
        if (retryCount < 2) {
            failedPages[pageKey] = retryCount + 1
            failedPagesData[pageKey] = [
                    pageNum: pageNum,
                    errorCode: errorCode,
                    payload: payload,
                    retryCount: retryCount + 1,
                    lastAttempt: new Date()
            ]
            LOG.info("{} error detected for page {}. Adding to retry queue. Retry attempt: {}",
                    errorCode, pageNum, retryCount + 1)
        }
    } else {
        // For 400 and other errors, just track them for reporting
        failedPagesData[pageKey] = [
                pageNum: pageNum,
                errorCode: errorCode,
                payload: payload,
                retryCount: 0,
                lastAttempt: new Date()
        ]
    }
}

def void generateTimingReport() {
    long initialProcessingTime = (initialProcessingEndTime - scriptStartTime) / 1000
    long totalTimeWithRetries = (retryEndTime - scriptStartTime) / 1000
    long retryProcessingTime = (retryEndTime - initialProcessingEndTime) / 1000

    LOG.info("=== EXECUTION TIME REPORT ===")
    LOG.info("Initial Processing:")
    LOG.info("  - Total pages processed: {}", totalProcessedPages)
    LOG.info("  - Time taken: {} seconds ({} minutes)",
            initialProcessingTime,
            String.format("%.2f", initialProcessingTime/60.0))

    LOG.info("\nRetry Processing:")
    LOG.info("  - Total pages retried: {}", totalRetryPages)
    LOG.info("  - Time taken: {} seconds ({} minutes)",
            retryProcessingTime,
            String.format("%.2f", retryProcessingTime/60.0))

    LOG.info("\nTotal Execution:")
    LOG.info("  - Total time (including retries): {} seconds ({} minutes)",
            totalTimeWithRetries,
            String.format("%.2f", totalTimeWithRetries/60.0))
    if(totalProcessedPages > 0 || totalRetryPages > 0 ) {
        LOG.info("  - Average time per page: {} seconds",
                String.format("%.2f", totalTimeWithRetries / (totalProcessedPages + totalRetryPages)))
    }
    LOG.info("=== END TIMING REPORT ===")
}

def void retryFailedPages() {

    // Ensure that failedPages is defined and initialized
    if (!binding.hasVariable("failedPages") || failedPages == null) {
        LOG.info("No failed pages data available.")
        return
    }
    // Retry failed pages
    totalRetryPages = failedPages.size()
    // Filter both 503 and UNKNOWN errors for retry
    def pagesToRetry = failedPages.findAll { it.key.endsWith("_503") || it.key.endsWith("_UNKNOWN") }

    if (pagesToRetry.isEmpty()) {
        LOG.info("No 503 or UNKNOWN errors to retry")
        generateFailureReport(failedPagesData)
        return
    }

    def maxRetries = 2 // Maximum number of retry attempts
    def currentRetry = 1

    while (currentRetry <= maxRetries && !pagesToRetry.isEmpty()) {
        LOG.info("Starting retry attempt {} of {} for {} pages with 503/UNKNOWN errors after 1 minute wait",
                currentRetry, maxRetries, pagesToRetry.size())
        Thread.sleep(60 * 1000) // 1 minute wait

        pagesToRetry.each { pageKey, retryCount ->
            def pageData = failedPagesData[pageKey]
            if (!pageData) return

            try {
                // Directly retry the failed payload
                def payload = pageData.payload
                def apiResult = createODataOperation(TARGET_ODATA_URL + "/" + ODATA_CONTEXT + "/" + ODATA_CONTEXT_TYPE, payload)

                // If successful, remove from failed tracking
                failedPages.remove(pageKey)
                failedPagesData.remove(pageKey)
                LOG.info("Successfully processed failed payload for page {} on retry attempt {}",
                        pageData.pageNum, currentRetry)
            } catch (Exception e) {
                def errorCode = getErrorCode(e)
                LOG.error("Page {} failed on retry attempt {} with error code: {}",
                        pageData.pageNum, currentRetry, errorCode)

                if (currentRetry == maxRetries || !(errorCode in ["503", "UNKNOWN"])) {
                    // On final retry or non-retryable error, update the error status
                    failedPages.remove(pageKey)
                    failedPagesData.remove(pageKey)
                    handleFailedPage(pageData.pageNum, errorCode, pageData.payload)
                }
            }
        }

        // Update pagesToRetry for next iteration
        pagesToRetry = failedPages.findAll { it.key.endsWith("_503") || it.key.endsWith("_UNKNOWN") }
        currentRetry++
    }

    // Generate final failure report after all retries
    generateFailureReport(failedPagesData)
}

def void generateFailureReport(Map failedPagesData) {
    if (failedPagesData.isEmpty()) {
        LOG.info("No failed pages to report!")
        return
    }

    LOG.error("=== FINAL FAILURE REPORT ===")

    // Group failed pages by error code
    def errorGroups = failedPagesData.values().groupBy { it.errorCode }

    // Report all error types
    ["400", "503", "UNKNOWN", "401", "404", "TIMEOUT", "CONNECTION_REFUSED"].each { errorCode ->
        if (errorGroups[errorCode]) {
            LOG.error("Pages failed with {} error{}:",
                    errorCode,
                    errorCode in ["503", "UNKNOWN"] ? " (after all retries)" : "")
            errorGroups[errorCode].sort { it.pageNum }.each { it ->
                LOG.error("  Page: {} (Retry count: {}) Payload: {}",
                        it.pageNum, it.retryCount, it.payload)
            }
        }
    }

    LOG.error("\nSummary:")
    errorGroups.each { errorCode, pages ->
        LOG.error("  {}: {} pages", errorCode, pages.size())
    }

    // Calculate retry statistics
    def totalRetried = failedPagesData.values().count { it.retryCount > 0 }
    def successfulRetries = totalRetryPages - failedPagesData.values().count {
        it.errorCode in ["503", "UNKNOWN"]
    }

    LOG.error("\nRetry Statistics:")
    LOG.error("  Total pages that needed retry: {}", totalRetryPages)
    LOG.error("  Successfully recovered after retry: {}", successfulRetries)
    LOG.error("  Failed after all retries: {}", totalRetryPages - successfulRetries)

    LOG.error("=== END FAILURE REPORT ===")
}

def processAllPages() {
    scriptStartTime = System.currentTimeMillis()
    LOG.info("Starting Migration Script Process For B2B Unit")
    def paginationData = getPagination()
    totalProcessedPages = 0  // Initialize counter
    paginationData.setCurrentPage(1882);

    do {
        def searchPageData = getSearchPageData()
        searchPageData.setPagination(paginationData)
        LOG.info("B2BUnit Migration - Current Page :" + paginationData.getCurrentPage())
        SearchPageData<JnJB2BUnitModel> results = listAll(MODIFIED_DELTA_DATE, searchPageData)
        if(results.getPagination().getCurrentPage() == 0){
            LOG.info("B2BUnit Migration - Number of B2B Units to be Processed: " + results.getPagination().totalNumberOfResults)
            LOG.info("B2BUnit Migration - Total Number of Pages to be Processed :" + results.getPagination().getNumberOfPages())

        }

        //if(totalProcessedPages == 2000){
            //LOG.info("Terminating Migration after 2000th iteration Current Iteration:{}",paginationData.getCurrentPage())
            //break
        //}
        paginationData = results.getPagination()
        if(results != null && CollectionUtils.isNotEmpty(results.getResults())) {
            totalProcessedPages++  // Increment counter for each processed page
            LOG.info("B2BUnit Migration - Elements to be processed:" + results.getResults().size())
            processPages(results.getPagination(), results.getResults())
        }else{
            LOG.info("B2BUnit Migration- No Elements to be processed")
        }
        LOG.info("B2BUnit Migration - Pages processed so far: {}", totalProcessedPages)
        LOG.info("B2BUnit Migration - Execution wait for {} seconds", DEFAULT_MAX_WAIT_SECONDS)
        TimeUnit.SECONDS.sleep(DEFAULT_MAX_WAIT_SECONDS)
        paginationData.setCurrentPage(paginationData.getCurrentPage()+1)
    } while(paginationData.getHasNext())

    LOG.info("B2BUnit Migration - migration completed. Total pages processed: {}", totalProcessedPages)
    initialProcessingEndTime = System.currentTimeMillis()
    retryFailedPages()
    retryEndTime = System.currentTimeMillis()
    // Generate final timing report
    generateTimingReport()
}


//--------------------------------------------------------main---------------------------------------------------------------------------------------------

LOG.info("Starting Migration Script Process For B2B Unit")
// Execute the migration
processAllPages()
//==================================================================================================================================================================================
// Class definitions to populate the data as per data mapping
class ODataLocalizedJnJB2BUnit {
    String locName // Nullable String
    String alphaName // Nullable String
    String language // Not Nullable String

    // Getters and Setters
    String getLocName() { return locName }
    void setLocName(String locName) { this.locName = locName }

    String getAlphaName() { return alphaName }
    void setAlphaName(String alphaName) { this.alphaName = alphaName }

    String getLanguage() { return language }
    void setLanguage(String language) { this.language = language }
}

class ODataJnJB2BUnit {
    String billingBlock // Nullable Boolean
    String salesOrg // Nullable String
    String locName // Nullable String
    String shortCustName // Nullable String
    String name // Nullable String
    String customerGroup // Nullable String
    String shippingCondition // Nullable String
    String sourceSysId // Nullable String
    String taxId // Nullable String
    String active // Nullable Boolean
    String orderBlock // Nullable Boolean
    String centralSalesBlock // Nullable Boolean
    String deliveryBlock // Nullable Boolean
    String deletionFlag // Nullable Boolean
    String alphaName // Nullable String
    String classOfTrade // Nullable String
    String sendMethod // Nullable String
    String globalLocNo // Nullable String
    String salesRepLocNo // Nullable String
    String uid // Not Nullable String
    String businessSector
    String integrationKey // Not Nullable String
    ODataAddress contactAddress // Nullable Address
    String indicator

    // Navigation Properties
    ODataCurrency currency // Nullable Currency
    ODataWarehouse plant // Nullable Warehouse
    List<ODataAddress> addresses // Nullable List of Addresses
    List<ODataJnJPartnerFunction> partnerFunction // Nullable List of JnJPartnerFunction
    ODataCountry country // Nullable Country
    ODataJnJGlobalTermsOfPayment termsOfPayment // Nullable JnJGlobalTermsOfPayment
    List<ODataJnJGlobalCustomerLicense> licenses // Nullable List of JnJGlobalCustomerLicense
    List<ODataJnJSalesOffice> salesOffice // Nullable List of JnJSalesOffice
    List<ODataGroup> groups // Nullable List of Groups
    List<ODataLocalizedJnJB2BUnit> localizedAttributes // Nullable List of Localized___JnJB2BUnit
    List<ODataSalesOrgCustomer> salesOrgCustomers // Nullable List of SalesOrgCustomer
    List<ODataAddress> dropShipAddresses // Nullable List of Addresses

    // Getters and Setters
    String getBillingBlock() { return billingBlock }
    void setBillingBlock(String billingBlock) { this.billingBlock = billingBlock }

    String getIndicator() { return indicator }
    void setIndicator(String indicator) { this.indicator = indicator }

    String getBusinessSector() { return businessSector }
    void setBusinessSector(String businessSector) { this.businessSector = businessSector }

    String getSalesOrg() { return salesOrg }
    void setSalesOrg(String salesOrg) { this.salesOrg = salesOrg }

    String getLocName() { return locName }
    void setLocName(String locName) { this.locName = locName }

    String getShortCustName() { return shortCustName }
    void setShortCustName(String shortCustName) { this.shortCustName = shortCustName }

    String getName() { return name }
    void setName(String name) { this.name = name }

    String getCustomerGroup() { return customerGroup }
    void setCustomerGroup(String customerGroup) { this.customerGroup = customerGroup }

    String getShippingCondition() { return shippingCondition }
    void setShippingCondition(String shippingCondition) { this.shippingCondition = shippingCondition }

    String getSourceSysId() { return sourceSysId }
    void setSourceSysId(String sourceSysId) { this.sourceSysId = sourceSysId }

    String getTaxId() { return taxId }
    void setTaxId(String taxId) { this.taxId = taxId }

    String getActive() { return active }
    void setActive(String active) { this.active = active }

    String getOrderBlock() { return orderBlock }
    void setOrderBlock(String orderBlock) { this.orderBlock = orderBlock }

    String getCentralSalesBlock() { return centralSalesBlock }
    void setCentralSalesBlock(String centralSalesBlock) { this.centralSalesBlock = centralSalesBlock }

    String getDeliveryBlock() { return deliveryBlock }
    void setDeliveryBlock(String deliveryBlock) { this.deliveryBlock = deliveryBlock }

    String getDeletionFlag() { return deletionFlag }
    void setDeletionFlag(String deletionFlag) { this.deletionFlag = deletionFlag }

    String getAlphaName() { return alphaName }
    void setAlphaName(String alphaName) { this.alphaName = alphaName }

    String getClassOfTrade() { return classOfTrade }
    void setClassOfTrade(String classOfTrade) { this.classOfTrade = classOfTrade }

    String getSendMethod() { return sendMethod }
    void setSendMethod(String sendMethod) { this.sendMethod = sendMethod }

    String getGlobalLocNo() { return globalLocNo }
    void setGlobalLocNo(String globalLocNo) { this.globalLocNo = globalLocNo }

    String getSalesRepLocNo() { return salesRepLocNo }
    void setSalesRepLocNo(String salesRepLocNo) { this.salesRepLocNo = salesRepLocNo }

    String getUid() { return uid }
    void setUid(String uid) { this.uid = uid }

    String getIntegrationKey() { return integrationKey }
    void setIntegrationKey(String integrationKey) { this.integrationKey = integrationKey }

    ODataAddress getContactAddress() { return contactAddress }
    void setContactAddress(ODataAddress contactAddress) { this.contactAddress = contactAddress }

    ODataCurrency getCurrency() { return currency }
    void setCurrency(ODataCurrency currency) { this.currency = currency }

    ODataWarehouse getPlant() { return plant }
    void setPlant(ODataWarehouse plant) { this.plant = plant }

    List<ODataAddress> getAddresses() { return addresses }
    void setAddresses(List<ODataAddress> addresses) { this.addresses = addresses }

    List<ODataJnJPartnerFunction> getPartnerFunction() { return partnerFunction }
    void setPartnerFunction(List<ODataJnJPartnerFunction> partnerFunction) { this.partnerFunction = partnerFunction }

    ODataCountry getCountry() { return country }
    void setCountry(ODataCountry country) { this.country = country }

    ODataJnJGlobalTermsOfPayment getTermsOfPayment() { return termsOfPayment }
    void setTermsOfPayment(ODataJnJGlobalTermsOfPayment termsOfPayment) { this.termsOfPayment = termsOfPayment }

    List<ODataJnJGlobalCustomerLicense> getLicenses() { return licenses }
    void setLicenses(List<ODataJnJGlobalCustomerLicense> licenses) { this.licenses = licenses }

    List<ODataJnJSalesOffice> getSalesOffice() { return salesOffice }
    void setSalesOffice(List<ODataJnJSalesOffice> salesOffice) { this.salesOffice = salesOffice }

    List<ODataGroup> getGroups() { return groups }
    void setGroups(List<ODataGroup> groups) { this.groups = groups }

    List<ODataLocalizedJnJB2BUnit> getLocalizedAttributes() { return localizedAttributes }
    void setLocalizedAttributes(List<ODataLocalizedJnJB2BUnit> localizedAttributes) { this.localizedAttributes = localizedAttributes }

    List<ODataSalesOrgCustomer> getSalesOrgCustomers() { return salesOrgCustomers }
    void setSalesOrgCustomers(List<ODataSalesOrgCustomer> salesOrgCustomers) { this.salesOrgCustomers = salesOrgCustomers }

    List<ODataAddress> getDropShipAddresses() { return dropShipAddresses }
    void setDropShipAddresses(List<ODataAddress> dropShipAddresses) { this.dropShipAddresses = dropShipAddresses }
}

class ODataWarehouse {
    String code // Not Nullable String
    String integrationKey // Not Nullable String

    // Getters and Setters
    String getCode() { return code }
    void setCode(String code) { this.code = code }

    String getIntegrationKey() { return integrationKey }
    void setIntegrationKey(String integrationKey) { this.integrationKey = integrationKey }
}

class ODataJnJOdataB2BUnit {
    String id // Not Nullable String
    String integrationKey // Not Nullable String

    // Navigation Property
    List<ODataJnJB2BUnit> b2bunits // Not Nullable List of JnJB2BUnit

    // Getters and Setters
    String getId() { return id }
    void setId(String id) { this.id = id }

    String getIntegrationKey() { return integrationKey }
    void setIntegrationKey(String integrationKey) { this.integrationKey = integrationKey }

    List<ODataJnJB2BUnit> getB2bunits() { return b2bunits }
    void setB2bunits(List<ODataJnJB2BUnit> b2bunits) { this.b2bunits = b2bunits }
}

class ODataAddress {
    String postalcode // Nullable String
    Boolean unloadingAddress // Nullable Boolean
    String countyName // Nullable String
    Boolean contactAddress // Nullable Boolean
    Boolean shippingAddress // Nullable Boolean
    String firstname // Nullable String
    String lastname // Nullable String
    String pobox // Nullable String
    Boolean endUserAddress // Nullable Boolean
    String building // Nullable String
    String town // Nullable String
    String district // Nullable String
    Boolean active // Nullable Boolean
    String publicKey // Nullable String
    Boolean stockPartnerAddress // Nullable Boolean
    String whatsAppNumber // Nullable String
    String sourceSysId // Nullable String
    Boolean billingAddress // Nullable Boolean
    String jnJAddressId // Nullable String
    String streetnumber // Nullable String
    String poBoxPostalCode // Nullable String
    String divisionCode // Nullable String
    String email // Nullable String
    String phone1 // Nullable String
    String cellphone // Nullable String
    String line2 // Nullable String
    String phone2 // Nullable String
    String line1 // Nullable String
    String line4 // Nullable String
    String line3 // Nullable String
    String company // Nullable String
    String streetname // Nullable String
    Boolean visibleInAddressBook // Nullable Boolean
    Boolean payFromAddress
    String department // Nullable String
    String integrationKey // Not Nullable String


    // Navigation Properties
    ODataCountry country // Nullable Country
    ODataTitle title // Nullable Title
    ODataRegion region // Nullable Region

    // Getters and Setters
    String getPostalcode() { return postalcode }
    void setPostalcode(String postalcode) { this.postalcode = postalcode }



    Boolean getUnloadingAddress() { return unloadingAddress }
    void setUnloadingAddress(Boolean unloadingAddress) { this.unloadingAddress = unloadingAddress }

    String getCountyName() { return countyName }
    void setCountyName(String countyName) { this.countyName = countyName }

    Boolean getContactAddress() { return contactAddress }
    void setContactAddress(Boolean contactAddress) { this.contactAddress = contactAddress }

    Boolean getShippingAddress() { return shippingAddress }
    void setShippingAddress(Boolean shippingAddress) { this.shippingAddress = shippingAddress }

    Boolean getPayFromAddress() { return payFromAddress }
    void setPayFromAddress(Boolean payFromAddress) { this.payFromAddress = payFromAddress }

    String getFirstname() { return firstname }
    void setFirstname(String firstname) { this.firstname = firstname }

    String getLastname() { return lastname }
    void setLastname(String lastname) { this.lastname = lastname }

    String getPobox() { return pobox }
    void setPobox(String pobox) { this.pobox = pobox }

    Boolean getEndUserAddress() { return endUserAddress }
    void setEndUserAddress(Boolean endUserAddress) { this.endUserAddress = endUserAddress }

    String getBuilding() { return building }
    void setBuilding(String building) { this.building = building }

    String getTown() { return town }
    void setTown(String town) { this.town = town }

    String getDistrict() { return district }
    void setDistrict(String district) { this.district = district }

    Boolean getActive() { return active }
    void setActive(Boolean active) { this.active = active }

    String getPublicKey() { return publicKey }
    void setPublicKey(String publicKey) { this.publicKey = publicKey }

    Boolean getStockPartnerAddress() { return stockPartnerAddress }
    void setStockPartnerAddress(Boolean stockPartnerAddress) { this.stockPartnerAddress = stockPartnerAddress }

    String getWhatsAppNumber() { return whatsAppNumber }
    void setWhatsAppNumber(String whatsAppNumber) { this.whatsAppNumber = whatsAppNumber }

    String getSourceSysId() { return sourceSysId }
    void setSourceSysId(String sourceSysId) { this.sourceSysId = sourceSysId }

    Boolean getBillingAddress() { return billingAddress }
    void setBillingAddress(Boolean billingAddress) { this.billingAddress = billingAddress }

    String getJnJAddressId() { return jnJAddressId }
    void setJnJAddressId(String jnJAddressId) { this.jnJAddressId = jnJAddressId }

    String getStreetnumber() { return streetnumber }
    void setStreetnumber(String streetnumber) { this.streetnumber = streetnumber }

    String getPoBoxPostalCode() { return poBoxPostalCode }
    void setPoBoxPostalCode(String poBoxPostalCode) { this.poBoxPostalCode = poBoxPostalCode }

    String getDivisionCode() { return divisionCode }
    void setDivisionCode(String divisionCode) { this.divisionCode = divisionCode }

    String getEmail() { return email }
    void setEmail(String email) { this.email = email }

    String getPhone1() { return phone1 }
    void setPhone1(String phone1) { this.phone1 = phone1 }

    String getCellphone() { return cellphone }
    void setCellphone(String cellphone) { this.cellphone = cellphone }

    String getLine2() { return line2 }
    void setLine2(String line2) { this.line2 = line2 }

    String getPhone2() { return phone2 }
    void setPhone2(String phone2) { this.phone2 = phone2 }

    String getLine1() { return line1 }
    void setLine1(String line1) { this.line1 = line1 }

    String getLine4() { return line4 }
    void setLine4(String line4) { this.line4 = line4 }

    String getLine3() { return line3 }
    void setLine3(String line3) { this.line3 = line3 }

    String getCompany() { return company }
    void setCompany(String company) { this.company = company }

    String getStreetname() { return streetname }
    void setStreetname(String streetname) { this.streetname = streetname }

    Boolean getVisibleInAddressBook() { return visibleInAddressBook }
    void setVisibleInAddressBook(Boolean visibleInAddressBook) { this.visibleInAddressBook = visibleInAddressBook }

    String getDepartment() { return department }
    void setDepartment(String department) { this.department = department }

    String getIntegrationKey() { return integrationKey }
    void setIntegrationKey(String integrationKey) { this.integrationKey = integrationKey }

    ODataCountry getCountry() { return country }
    void setCountry(ODataCountry country) { this.country = country }

    ODataTitle getTitle() { return title }
    void setTitle(ODataTitle title) { this.title = title }

    ODataRegion getRegion() { return region }
    void setRegion(ODataRegion region) { this.region = region }
}

class ODataJnJGlobalCustomerLicense {
    String validTill // Nullable String (Date)
    String licenseNumber // Nullable String
    String validFrom // Nullable String (Date)
    String integrationKey // Not Nullable String

    // Navigation Properties
    ODataJnJB2BUnit b2bunit // Nullable JnJB2BUnit
    ODataJnJGlobalCustomerLicenseType licenseType // Not Nullable JnJGlobalCustomerLicenseType

    // Getters and Setters
    String getValidTill() { return validTill }
    void setValidTill(String validTill) { this.validTill = validTill }

    String getLicenseNumber() { return licenseNumber }
    void setLicenseNumber(String licenseNumber) { this.licenseNumber = licenseNumber }

    String getValidFrom() { return validFrom }
    void setValidFrom(String validFrom) { this.validFrom = validFrom }

    String getIntegrationKey() { return integrationKey }
    void setIntegrationKey(String integrationKey) { this.integrationKey = integrationKey }

    ODataJnJB2BUnit getB2bunit() { return b2bunit }
    void setB2bunit(ODataJnJB2BUnit b2bunit) { this.b2bunit = b2bunit }

    ODataJnJGlobalCustomerLicenseType getLicenseType() { return licenseType }
    void setLicenseType(ODataJnJGlobalCustomerLicenseType licenseType) { this.licenseType = licenseType }
}

class ODataJnJGlobalTermsOfPayment {
    String code // Nullable String
    String integrationKey // Not Nullable String

    // Getters and Setters
    String getCode() { return code }
    void setCode(String code) { this.code = code }

    String getIntegrationKey() { return integrationKey }
    void setIntegrationKey(String integrationKey) { this.integrationKey = integrationKey }
}

class ODataCurrency {
    String isocode // Not Nullable String
    String integrationKey // Not Nullable String

    // Getters and Setters
    String getIsocode() { return isocode }
    void setIsocode(String isocode) { this.isocode = isocode }

    String getIntegrationKey() { return integrationKey }
    void setIntegrationKey(String integrationKey) { this.integrationKey = integrationKey }
}

class ODataJnJSalesOffice {
    String code // Nullable String
    String integrationKey // Not Nullable String

    // Getters and Setters
    String getCode() { return code }
    void setCode(String code) { this.code = code }

    String getIntegrationKey() { return integrationKey }
    void setIntegrationKey(String integrationKey) { this.integrationKey = integrationKey }
}

class ODataRegion {
    String isocode // Not Nullable String
    String integrationKey // Not Nullable String

    // Navigation Property
    ODataCountry country // Not Nullable Country

    // Getters and Setters
    String getIsocode() { return isocode }
    void setIsocode(String isocode) { this.isocode = isocode }

    String getIntegrationKey() { return integrationKey }
    void setIntegrationKey(String integrationKey) { this.integrationKey = integrationKey }

    ODataCountry getCountry() { return country }
    void setCountry(ODataCountry country) { this.country = country }
}

class ODataCountry {
    String isocode // Not Nullable String
    String integrationKey // Not Nullable String

    // Getters and Setters
    String getIsocode() { return isocode }
    void setIsocode(String isocode) { this.isocode = isocode }

    String getIntegrationKey() { return integrationKey }
    void setIntegrationKey(String integrationKey) { this.integrationKey = integrationKey }
}

class ODataJnJGlobalCustomerLicenseType {
    String code // Not Nullable String
    String distChannel // Not Nullable String
    String division // Not Nullable String
    String salesOrg // Not Nullable String
    String integrationKey // Not Nullable String

    // Getters and Setters
    String getCode() { return code }
    void setCode(String code) { this.code = code }

    String getDistChannel() { return distChannel }
    void setDistChannel(String distChannel) { this.distChannel = distChannel }

    String getDivision() { return division }
    void setDivision(String division) { this.division = division }

    String getSalesOrg() { return salesOrg }
    void setSalesOrg(String salesOrg) { this.salesOrg = salesOrg }

    String getIntegrationKey() { return integrationKey }
    void setIntegrationKey(String integrationKey) { this.integrationKey = integrationKey }
}

class ODataJnJPartnerFunctionCode {
    String code // Not Nullable String
    String integrationKey // Not Nullable String

    // Getters and Setters
    String getCode() { return code }
    void setCode(String code) { this.code = code }

    String getIntegrationKey() { return integrationKey }
    void setIntegrationKey(String integrationKey) { this.integrationKey = integrationKey }
}

class ODataJnJPartnerFunction {
    String salesOrg // Nullable String
    String divison // Nullable String
    String accountId // Not Nullable String
    String distChannel // Nullable String
    String indicator // Nullable Boolean
    String integrationKey // Not Nullable String

    // Navigation Properties
    ODataJnJPartnerFunctionCode partnerFunctionCode // Not Nullable JnJPartnerFunctionCode
    ODataJnJB2BUnit b2bunit // Nullable JnJB2BUnit

    // Getters and Setters
    String getSalesOrg() { return salesOrg }
    void setSalesOrg(String salesOrg) { this.salesOrg = salesOrg }

    String getDivison() { return divison }
    void setDivison(String divison) { this.divison = divison }

    String getAccountId() { return accountId }
    void setAccountId(String accountId) { this.accountId = accountId }

    String getDistChannel() { return distChannel }
    void setDistChannel(String distChannel) { this.distChannel = distChannel }

    String getIndicator() { return indicator }
    void setIndicator(String indicator) { this.indicator = indicator }

    String getIntegrationKey() { return integrationKey }
    void setIntegrationKey(String integrationKey) { this.integrationKey = integrationKey }

    ODataJnJPartnerFunctionCode getPartnerFunctionCode() { return partnerFunctionCode }
    void setPartnerFunctionCode(ODataJnJPartnerFunctionCode partnerFunctionCode) { this.partnerFunctionCode = partnerFunctionCode }

    ODataJnJB2BUnit getB2bunit() { return b2bunit }
    void setB2bunit(ODataJnJB2BUnit b2bunit) { this.b2bunit = b2bunit }
}

class ODataGroup {
    String uid // Not Nullable String
    String integrationKey // Not Nullable String

    // Getters and Setters
    String getUid() { return uid }
    void setUid(String uid) { this.uid = uid }

    String getIntegrationKey() { return integrationKey }
    void setIntegrationKey(String integrationKey) { this.integrationKey = integrationKey }
}

class ODataTitle {
    String code // Not Nullable String
    String integrationKey // Not Nullable String

    // Getters and Setters
    String getCode() { return code }
    void setCode(String code) { this.code = code }

    String getIntegrationKey() { return integrationKey }
    void setIntegrationKey(String integrationKey) { this.integrationKey = integrationKey }
}

class ODataSalesOrgCustomer {
    String priceListId
    String salesOrg
    String distributionChannel
    String sourceSysId
    String division
    String blockCode
    String businessSector
    String customerGroup
    ODataUserPriceGroup userPriceGroup
    String code

    // Getters and Setters
    String getCode() { return code }
    void setCode(String code) { this.code = code }

    // Getters and Setters
    String getPriceListId() { return priceListId }
    void setPriceListId(String priceListId) { this.priceListId = priceListId }

    String getSalesOrg() { return salesOrg }
    void setSalesOrg(String salesOrg) { this.salesOrg = salesOrg }

    String getDistributionChannel() { return distributionChannel }
    void setDistributionChannel(String distributionChannel) { this.distributionChannel = distributionChannel }

    String getSourceSysId() { return sourceSysId }
    void setSourceSysId(String sourceSysId) { this.sourceSysId = sourceSysId }

    String getDivision() { return division }
    void setDivision(String division) { this.division = division }

    String getBlockCode() { return blockCode }
    void setBlockCode(String blockCode) { this.blockCode = blockCode }

    String getBusinessSector() { return businessSector }
    void setBusinessSector(String businessSector) { this.businessSector = businessSector }

    String getCustomerGroup() { return customerGroup }
    void setCustomerGroup(String customerGroup) { this.customerGroup = customerGroup }

    ODataUserPriceGroup getUserPriceGroup() { return userPriceGroup }
    void setUserPriceGroup(ODataUserPriceGroup userPriceGroup) { this.userPriceGroup = userPriceGroup }
}

class ODataUserPriceGroup {
    String code

    // Getters and Setters
    String getCode() { return code }
    void setCode(String code) { this.code = code }
}
