import com.jnj.core.model.JnJB2BUnitModel
import com.jnj.global.core.model.JnJPartnerFunctionModel;
import de.hybris.platform.core.servicelayer.data.PaginationData
import de.hybris.platform.core.servicelayer.data.SearchPageData
import de.hybris.platform.servicelayer.model.ModelService
import de.hybris.platform.servicelayer.search.FlexibleSearchQuery
import de.hybris.platform.servicelayer.search.FlexibleSearchService
import de.hybris.platform.servicelayer.search.paginated.PaginatedFlexibleSearchParameter
import de.hybris.platform.servicelayer.search.paginated.PaginatedFlexibleSearchService
import groovy.transform.Field
import org.apache.commons.collections.CollectionUtils
import org.slf4j.Logger
import org.slf4j.LoggerFactory

@Field final Logger LOG = LoggerFactory.getLogger(getClass())
@Field final ModelService modelService = spring.getBean("modelService")
@Field final FlexibleSearchService flexibleSearchService = spring.getBean("flexibleSearchService")
@Field final PaginatedFlexibleSearchService paginatedFlexibleSearchService = spring.getBean("paginatedFlexibleSearchService")
@Field final int DEFAULT_MAX_PAGE_SIZE = 500
@Field final String SALES_ORG = 'US01'
@Field final String DISTRIBUTION_CHANNEL = '10'

@Field final String FIND_ALL_QUERY = """
    SELECT {pk} 
    FROM {JnJB2BUnit! AS b} 
    WHERE {b:businessSector} = 'MDD_NA_US'  
    AND {b:indicator} = 'International_Affiliate' 
    ORDER BY {b:uid}
"""

@Field final String FIND_BRIDGE_B2BUNIT_QUERY = """
    SELECT {pk} 
    FROM {JnJB2BUnit! AS b} 
    WHERE {b:uid} LIKE CONCAT(?uid, '%')
"""

def SearchPageData<JnJB2BUnitModel> listAll(SearchPageData searchPageData) {
    try {
        FlexibleSearchQuery searchQuery = new FlexibleSearchQuery(FIND_ALL_QUERY)
        searchQuery.addQueryParameter("businessSector", "MDD_NA_US")
        PaginatedFlexibleSearchParameter parameter = new PaginatedFlexibleSearchParameter()
        parameter.setFlexibleSearchQuery(searchQuery)
        parameter.setSearchPageData(searchPageData)
        return paginatedFlexibleSearchService.search(parameter)
    } catch(Exception e) {
        LOG.error("Error fetching B2B units: {}", e.getMessage())
        throw e
    }
}

def JnJB2BUnitModel findBridgeB2BUnit(String baseUid) {
    try {
        String bridgeUid = baseUid + "|" + SALES_ORG 
        FlexibleSearchQuery query = new FlexibleSearchQuery(FIND_BRIDGE_B2BUNIT_QUERY)
        query.addQueryParameter("uid", bridgeUid)
        return flexibleSearchService.searchUnique(query)
    } catch(Exception e) {
        LOG.debug("No bridge B2BUnit found for baseUid: {}", baseUid)
        return null
    }
}

def SearchPageData getSearchPageData() {
    SearchPageData searchPageData = new SearchPageData()
    searchPageData.setPagination(getPagination())
    return searchPageData
}

def PaginationData getPagination() {
    PaginationData paginationData = new PaginationData()
    paginationData.setCurrentPage(0)
    paginationData.setPageSize(DEFAULT_MAX_PAGE_SIZE)
    paginationData.setNeedsTotal(true)
    return paginationData
}

def deletePartnerFunctions(JnJB2BUnitModel bridgeB2BUnit) {
    List<JnJPartnerFunctionModel> partnerFunctions = bridgeB2BUnit.getPartnerFunction()
    if(CollectionUtils.isNotEmpty(partnerFunctions)) {
        LOG.info("Deleting {} partner functions for bridge B2BUnit: {}", partnerFunctions.size(), bridgeB2BUnit.getUid())
        for(def partnerFunction : partnerFunctions) {
            try {
                modelService.remove(partnerFunction)
            } catch(Exception e) {
                LOG.error("Error deleting partner function for bridge B2BUnit: {}", bridgeB2BUnit.getUid(), e)
            }
        }
    }
}

def int getTotalB2BUnitsCount() {
    FlexibleSearchQuery countQuery = new FlexibleSearchQuery("""
        SELECT COUNT({b.pk}) as total 
        FROM {JnJB2BUnit! as b} 
        WHERE {b.businessSector} = ?businessSector
    """)
    countQuery.setResultClassList(Collections.singletonList(Integer.class));
    countQuery.addQueryParameter("businessSector", "MDD_NA_US")
    def searchResult = flexibleSearchService.search(countQuery)
    return searchResult.getResult().iterator().next();
}

def deleteB2BUnits(PaginationData paginationData, List<JnJB2BUnitModel> results) {
    try {
        for(JnJB2BUnitModel mainB2BUnit : results) {
            String baseUid = mainB2BUnit.getUid()
            LOG.info("Processing main B2BUnit with UID: {}", baseUid)
            
            modelService.refresh(mainB2BUnit)
            
            // First find and delete the bridge B2BUnit and its partner functions
            JnJB2BUnitModel bridgeB2BUnit = findBridgeB2BUnit(baseUid)
            if(bridgeB2BUnit != null) {
                String bridgeUid = bridgeB2BUnit.getUid()
                LOG.info("Found bridge B2BUnit: {}", bridgeUid)
                
                modelService.refresh(bridgeB2BUnit)
                
                // Delete partner functions associated with bridge B2BUnit
                deletePartnerFunctions(bridgeB2BUnit)
                
                // Delete the bridge B2BUnit
                LOG.info("Deleting bridge B2BUnit: {}", bridgeUid)
                modelService.remove(bridgeB2BUnit)
            }
            
            // Delete the main B2BUnit
            LOG.info("Deleting main B2BUnit: {}", baseUid)
            modelService.remove(mainB2BUnit)
        }
        
        // Force commit after each batch
        modelService.saveAll()
    } catch(Exception e) {
        LOG.error("Error deleting B2BUnits ", e)
        throw e
    }
}

// Main execution
LOG.info("Starting B2BUnit Deletion Process")
def initialCount = getTotalB2BUnitsCount()
LOG.info("Initial count of B2BUnits to delete: {}", initialCount)

def paginationData = getPagination()
def totalDeleted = 0
def totalProcessedPages = 0  // Initialize counter

try {
    while(true) {
        // Always reset to page 0 to get the next batch of remaining records
        paginationData.setCurrentPage(0)
        
        def searchPageData = getSearchPageData()
        searchPageData.setPagination(paginationData)
        
        SearchPageData<JnJB2BUnitModel> results = listAll(searchPageData)
        
        if(results == null || CollectionUtils.isEmpty(results.getResults())) {
            LOG.info("No more B2BUnits found to process")
            break
        }
        
        int batchSize = results.getResults().size()
        LOG.info("Processing {} B2BUnits in current batch", batchSize)
        deleteB2BUnits(results.getPagination(), results.getResults())
        totalDeleted += batchSize
        LOG.info("Total B2BUnits deleted so far: {}", totalDeleted)
        
        // Check if we've deleted everything
        def currentCount = getTotalB2BUnitsCount()
        if(currentCount == 0) {
            LOG.info("All B2BUnits have been deleted")
            break
        }
        
        LOG.info("Remaining B2BUnits to process: {}", currentCount)
    }
    
    def finalCount = getTotalB2BUnitsCount()
    LOG.info("=== Deletion Summary ===")
    LOG.info("Initial count: {}", initialCount)
    LOG.info("Final count: {}", finalCount)
    LOG.info("Total B2BUnits deleted: {}", totalDeleted)
    LOG.info("Actual reduction in database: {}", initialCount - finalCount)
    
    return "OK - Deleted " + totalDeleted + " B2BUnits"
    
} catch(Exception e) {
    LOG.error("Error during deletion process", e)
    return "ERROR - " + e.getMessage()
}




